'use client';

import React, { useMemo, useRef } from 'react';

import { BarChart, Bar, Rectangle, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

import { TBarWidgetProps, barWidgetSampleDataSchema } from '@/types/bar-widget-props';
import { useParentResizeObserver } from '@/hooks/use-parent-resize';
export type BarRenderProps = {
  id: string
} & TBarWidgetProps;

const chartConfig = {
  desktop: {
    label: "Desktop",
    color: "#2563eb",
  },
  mobile: {
    label: "Mobile",
    color: "#60a5fa",
  },
} satisfies ChartConfig

export function BarRender( { sampleData, barColor }: BarRenderProps) {
  const data = useMemo(() => {
    try {
      return barWidgetSampleDataSchema.parse(JSON.parse(sampleData));
    }catch(e) {
      console.error("barWidgetSampleDataSchema error", e);
    }
  }, [sampleData]);

  const ref = useRef<HTMLDivElement>(null);
  const dim = useParentResizeObserver(ref);

  // 计算 activeBar 的颜色（50% 透明度）
  const activeBarColor = useMemo(() => {
    if (!barColor) return 'rgba(255, 192, 203, 0.5)'; // 默认粉色 50% 透明度

    // 如果是 hex 颜色
    if (barColor.startsWith('#')) {
      const hex = barColor.slice(1);
      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);
      return `rgba(${r}, ${g}, ${b}, 0.5)`;
    }

    // 如果是 rgb 颜色，转换为 rgba
    if (barColor.startsWith('rgb(')) {
      return barColor.replace('rgb(', 'rgba(').replace(')', ', 0.5)');
    }

    // 如果已经是 rgba，修改透明度
    if (barColor.startsWith('rgba(')) {
      return barColor.replace(/,\s*[\d.]+\)$/, ', 0.5)');
    }

    // 其他情况，直接添加透明度
    return `${barColor}80`; // 添加 50% 透明度的 hex 后缀
  }, [barColor]);

  return (
    <ChartContainer ref={ref} config={chartConfig}
      style={{
        height: dim?.height || 150,
        width: dim?.width || 500,
      }}>
      <BarChart
        data={data}
        margin={{
          top: 5,
          right: 10,
          left: 10,
          bottom: 5,
        }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="x" />
        <YAxis />
        {/* <Tooltip /> */}
        <ChartTooltip
          content={
            <ChartTooltipContent
              className="w-[150px]"
              nameKey="views"
              labelFormatter={(value) => {
                return new Date(value).toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                  year: "numeric",
                })
              }}
            />
          }
        />
        {/* <Legend /> */}
        <ChartTooltip content={<ChartTooltipContent hideLabel />} />
            <ChartLegend content={<ChartLegendContent />} />
        <Bar
          dataKey="y"
          fill={barColor}
          maxBarSize={40}
          radius={[6, 6, 0, 0]}
          activeBar={<Rectangle fill={activeBarColor} />}
        />
      </BarChart>
    </ChartContainer>
  );
}
