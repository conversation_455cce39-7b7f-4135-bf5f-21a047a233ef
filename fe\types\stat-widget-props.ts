import { z } from 'zod';

export const statWidgetPropsSchema = z.object({
  title: z.string().min(1, '标题不能为空').default('单值'),
  fontSize: z.number().min(5, '字体大小不能小于5').max(80, '字体大小不能大于80').default(28),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '颜色格式不正确，必须是十六进制颜色').default('#165dff'),
  widgetName: z.string().min(1, '小部件名称不能为空').default('StatWidget'),
  unit: z.string().default(''),
  outputVarname: z.string().min(1, '输出变量名不能为空').default('output1'),
  sampleData: z.string().default('{"output1": {"num": 123456}}'),
})
export type TStatWidgetProps = z.infer<typeof statWidgetPropsSchema>;

export const statWidgetSampleDataSchema = z.record(z.string(), z.object({
  num: z.number().default(123456),
  num_color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '颜色格式不正确，必须是十六进制颜色').default('#165dff'),
}))
