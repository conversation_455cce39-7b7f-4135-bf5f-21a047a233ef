import { z } from 'zod';

export const barWidgetPropsSchema = z.object({
  title: z.string().min(1, '标题不能为空').default('折线图'),
  barColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '颜色格式不正确，必须是十六进制颜色').default('#165dff'),
  maxYValue: z.number().default(100),
  oputputVarname: z.string().min(1, '输出变量名不能为空').default('output1'),
  sampleData: z.string().default(' [{ "x": 1, "y": 1, "y_color": "#165dff"}, { "x":2, "y": 2, "y_color": "#165dff"}, { "x":3, "y": 2, "y_color": "#165dff"}, { "x":4, "y": 2, "y_color": "#165dff"} ]'),
})
export type TBarWidgetProps = z.infer<typeof barWidgetPropsSchema>;

export const barWidgetSampleDataSchema = z.array(z.object({
  x: z.coerce.number().default(0),
  y: z.coerce.number().default(0),
  y_color: z.string().optional(),
}))
