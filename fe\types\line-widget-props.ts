import { z } from 'zod';

export const lineWidgetPropsSchema = z.object({
  title: z.string().min(1, '标题不能为空').default('折线图'),
  lineStrokeWidth: z.number().min(2, '线宽度不能小于 2').max(20, '线宽度不能大于20').default(2),
  lineStrokeColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '颜色格式不正确，必须是十六进制颜色').default('#165dff'),
  widgetName: z.string().min(1, '小部件名称不能为空').default('lineWidget'),
  minYValue: z.number().default(0),
  maxYValue: z.number().default(100),
  xDataCount: z.number().min(1, 'X轴数据点数量不能小于1').max(2000, 'X轴数据点数量不能大于100').default(100),
  oputputVarname: z.string().min(1, '输出变量名不能为空').default('output1'),
  sampleData: z.string().default(' [{ "x": 1, "y": 1 }, { "x":2, "y": 2}, { "x":3, "y": 2}, { "x":4, "y": 2} ]'),
})
export type TLineWidgetProps = z.infer<typeof lineWidgetPropsSchema>;

export const lineWidgetSampleDataSchema = z.array(z.object({
  x: z.number().default(0),
  y: z.number().default(0),
}))
