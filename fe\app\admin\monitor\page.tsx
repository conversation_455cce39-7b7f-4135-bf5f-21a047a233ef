'use client';

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { DeviceIcon } from '@/components/icons';

import { useRouter } from "next/navigation";

import { Plus, Trash2, Pencil } from "lucide-react";

export default function Page() {
  const router = useRouter();

  const gotoScene = () => {
    router.push('/admin/monitor/scene')
  }

  return (
    <div className="flex flex-col gap-4 p-4">
      <div id="breadcrumb" className="flex flex-row gap-4 justify-between items-center">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <DeviceIcon className="w-4 h-4" />
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              场景
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-meuted-foreground font-semibold">场景列表</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button variant="outline" className=''>新建场景</Button>
      </div>

      <div className="grid grid-cols-3 xl:grid-cols-5 gap-4">
        <Card className="cursor-pointer justify-center items-center flex hover:shadow-lg">
          <Plus className="w-6 h-6 text-muted-foreground" />
          <h5 className="text-sm font-semibold">创建/复制场景</h5>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg group"
          onClick={gotoScene}
        >
          <CardHeader className=''>
            <CardTitle className='text-sm'>场景 4</CardTitle>
            <CardDescription>2025/03 - BTT单机风扇观测</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-sm text-muted-foreground'>图表数量: <span className="font-semibold">10</span></div>
          </CardContent>
          <CardFooter className="flex flex-row items-center justify-end gap-2">
            <Button variant="outline" size="sm">
              <Trash2 />
            </Button>
            <Button variant="default" size="sm">
              进入
            </Button>
          </CardFooter>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg group"
          onClick={gotoScene}
        >
          <CardHeader className=''>
            <CardTitle className='text-sm'>场景 5</CardTitle>
            <CardDescription>2025/03 - BTT单机风扇观测</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-sm text-muted-foreground'>图表数量: <span className="font-semibold">10</span></div>
          </CardContent>
          <CardFooter className="flex flex-row items-center justify-end gap-2">
            <Button variant="outline" size="sm">
              <Trash2 />
            </Button>
            <Button variant="default" size="sm">
              进入
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
