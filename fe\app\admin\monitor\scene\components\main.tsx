'use client';

import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

import React, { useRef, useEffect, useCallback } from 'react';
import GridLayout from 'react-grid-layout';

import { useAppStore, SceneStore, DialogTrackerStore } from '@/app/admin/store';
import { shallow } from 'zustand/shallow';
import { AddWidgetForm } from './add-widget-form';
import { Fullscreen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { type WidgetType } from '@/types';
import { WidgetWrapper } from './widget-wrapper';
import { StatRender, LineRender, BarRender } from './widgets';
import { isAnyDialogOpen } from '@/lib/utils';

import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { DeviceIcon } from '@/components/icons';

const selector = (state: SceneStore & DialogTrackerStore) => ({
  openDialogs: state.openDialogs,
  addEmptyWidget: state.addEmptyWidget,
  applyLayoutsChange: state.applyLayoutsChange,
  toLayout: state.widgets.map((widget) => ({
    i: widget.id,
    x: widget.x,
    y: widget.y,
    w: widget.w,
    h: widget.h,
  })),
  toWidgetsConfig: state.widgets.map((widget) => ({
    id: widget.id,
    type: widget.type,
    props: widget.props,
  })),
})

export function Main() {
  const { addEmptyWidget, applyLayoutsChange, toLayout, toWidgetsConfig } = useAppStore(selector, shallow);
  const ref = useRef<HTMLDivElement>(null);

  const [width, setWidth] = React.useState(1200);
  useEffect(() => {
    if (ref.current) {
      setWidth(ref.current.offsetWidth || 1200);
    }
    const widthHandle = () => { setWidth(ref.current?.offsetWidth || 1200); }
    window.addEventListener('resize', widthHandle);
    return () => { window.removeEventListener('resize', widthHandle); }
  }, [setWidth])
  const [showAddWidgetForm, setShowAddWidgetForm] = React.useState(false);

  const [hasDialogOver, setHasDialogOver] = React.useState(false);
  useEffect(() => {
    const interval = setInterval(() => {
      setHasDialogOver(isAnyDialogOpen())
    }, 300)

    return () => clearInterval(interval)
  },[])

  const renderWidgets = useCallback(
    () => {
      return toWidgetsConfig.map(widget => {
        const {id} = widget;
        const type = widget.type as WidgetType;
        const props = widget.props || {}  as unknown

        switch (type) {
          case 'stat':
            return (
              <div id={id} key={id} >
                <WidgetWrapper title={props.title} id={id}>
                  {/* @ts-expect-error "omit" */}
                  <StatRender id={id} {...props} />
                </WidgetWrapper>
              </div>
            )
          case 'line':
            return (
              <div id={id} key={id} >
                <WidgetWrapper title={props.title} id={id}>
                  {/* @ts-expect-error "omit" */}
                  <LineRender id={id} {...props} />
                </WidgetWrapper>
              </div>
            )
          case 'bar':
            return (
              <div id={id} key={id} >
                <WidgetWrapper title={props.title} id={id}>
                  {/* @ts-expect-error "omit" */}
                  <BarRender id={id} {...props} />
                </WidgetWrapper>
              </div>
            )
        }

        return null;
      })
    },  [toWidgetsConfig]);


  return (
    <div ref={ref} className="w-full h-full relative">
      <div id="breadcrumb" className="flex flex-row gap-4 justify-between items-center">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <DeviceIcon className="w-4 h-4" />
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              场景
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-meuted-foreground font-semibold">数据可视化</BreadcrumbPage>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-meuted-foreground font-semibold">场景 A</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div className='flex flex-row gap-2 items-center'>
          <Fullscreen className="w-4 h-4 inline-block mr-2" />
          <Button className='cursor-pointer rounded-none' onClick={() => setShowAddWidgetForm( true) }>新建图表</Button>
        </div>
      </div>

      <GridLayout
        className="layout mt-4"
        layout={toLayout}
        cols={12}
        margin={[10, 10]}
        containerPadding={[0, 0]}
        rowHeight={30}
        width={width}
        isDraggable={!hasDialogOver}
        isResizable={!hasDialogOver}
        onLayoutChange={applyLayoutsChange}
      >
        { renderWidgets() }
      </GridLayout>

      <AddWidgetForm isOpen={showAddWidgetForm} setIsOpen={setShowAddWidgetForm} onAddWidgetWithType={addEmptyWidget} />
    </div>
  )
}
